# Redis com Reconexão Automática - SEM PORTA EXTERNA

## 🏗️ Como construir e executar

### 1. Construir a imagem Docker:
```cmd
docker-compose build
```

### 2. Iniciar o container Redis:
```cmd
docker-compose up -d redis
```

### 3. Verificar logs:
```cmd
docker-compose logs -f redis
```

### 4. Testar conexão via rede Docker (container de teste):
```cmd
docker-compose --profile test up redis-test-client
```

### 5. Conectar de outro container na mesma rede:
```cmd
docker run --rm --network redis_network -it python:3.11-slim bash
# Dentro do container:
pip install redis
python -c "
import redis
r = redis.Redis(host='host', port=6379, username='username', password='password')
print(r.ping())
"
```

## 📊 Monitoramento

### Verificar status dos serviços:
```cmd
docker exec redis_container supervisorctl status
```

### Ver logs específicos:
```cmd
# Logs do Redis
docker exec redis_container tail -f /data/redis.log

# Logs do Connection Manager
docker exec redis_container tail -f /data/connection_manager.log

# Logs do Health Check
docker exec redis_container tail -f /data/healthcheck.log
```

### Acessar container:
```cmd
docker exec -it redis_container bash
```

## 🔧 Recursos Implementados

### Dentro do Container:
- ✅ **Supervisor** - Gerencia múltiplos processos
- ✅ **Redis Server** - Servidor principal com auto-restart
- ✅ **Connection Manager** - Script Python que monitora conexões
- ✅ **Health Check** - Verificação contínua de saúde
- ✅ **Auto Restart** - Reinicialização automática em caso de falha
- ✅ **Logs Centralizados** - Todos os logs em /data/
- ✅ **Rede Interna** - Redis acessível apenas via rede Docker

### Para Clientes na Rede Docker:
- ✅ **Acesso via Nome** - Conecte usando 'redis_container:6379'
- ✅ **Retry Automático** - Clientes tentam reconectar automaticamente
- ✅ **Delay Progressivo** - Aumenta intervalo entre tentativas
- ✅ **Keep-Alive** - Mantém conexões TCP ativas
- ✅ **Health Monitoring** - Verifica saúde da conexão
- ✅ **Fallback Logic** - Lógica de recuperação em falhas

## 🚨 Troubleshooting

### Container não inicia:
```cmd
docker-compose logs redis
```

### Redis não aceita conexões:
```cmd
docker exec redis_container redis-cli -u redis://username:password@localhost:6379 ping
```

### Verificar processos:
```cmd
docker exec redis_container ps aux
```

### Reiniciar serviços:
```cmd
docker exec redis_container supervisorctl restart all
```

## 📈 Teste de Performance

O script `test_external_client.py` executa testes contínuos mostrando:
- Taxa de sucesso das conexões
- Número de clientes conectados
- Tempo de uptime do servidor
- Estatísticas de reconexão

## 🔒 Segurança

- Usuário específico (username) com senha
- ACL configurado em users.acl
- Comandos perigosos desabilitados
- Logs de auditoria ativados
# redis_persisente
