services:
  redis:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: redis_cache
    networks:
      - uniqsuporte
    volumes:
      # Dados persistentes
      - /home/<USER>/data:/data
      # Arquivos de configuração (bind mounts, somente leitura)
      - /home/<USER>/redis.conf:/etc/redis/redis.conf:ro
      - /home/<USER>/users.acl:/etc/redis/users.acl:ro
    restart: unless-stopped
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    environment:
      - REDIS_HOST=127.0.0.1
      - REDIS_USER=uniqsuporte
      - REDIS_PASSWORD=Fublpt66
    healthcheck:
      test: ["CMD", "redis-cli", "-h", "127.0.0.1", "--user", "uniqsuporte", "--pass", "Fublpt66", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

networks:
  uniqsuporte:
    external: true
