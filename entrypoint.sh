#!/bin/bash
set -e

echo "🚀 Iniciando Redis Container com Gerenciador de Reconexão..."

# Criar usuário redis se não existir
if ! id "redis" &>/dev/null; then
    echo "Criando usuário redis..."
    adduser --system --group --no-create-home redis
fi

# Definir permissões corretas (somente /data sempre; configs apenas se graváveis)
chown -R redis:redis /data
chmod 755 /data

# Ajustar permissões dos arquivos de configuração somente se não forem montados como read-only
if [ -w /etc/redis/redis.conf ]; then
    chown redis:redis /etc/redis/redis.conf || true
    chmod 640 /etc/redis/redis.conf || true
else
    echo "Aviso: /etc/redis/redis.conf montado como somente leitura; pulando chown/chmod"
fi

if [ -w /etc/redis/users.acl ]; then
    chown redis:redis /etc/redis/users.acl || true
    chmod 640 /etc/redis/users.acl || true
else
    echo "Aviso: /etc/redis/users.acl montado como somente leitura; pulando chown/chmod"
fi

# Certificar que arquivos de configuração existem
if [ ! -f /etc/redis/redis.conf ]; then
    echo "❌ Arquivo redis.conf não encontrado!" | tee -a /data/redis_error.log
    exit 1
fi

if [ ! -f /etc/redis/users.acl ]; then
    echo "❌ Arquivo users.acl não encontrado!" | tee -a /data/redis_error.log
    exit 1
fi

echo "✅ Arquivos de configuração encontrados"

# Criar arquivos de log
touch /data/redis.log /data/redis_error.log /data/connection_manager.log /data/connection_manager_error.log
chmod 640 /data/*.log
chown redis:redis /data/*.log

# Criar diretório e log do Supervisor
mkdir -p /var/log/supervisor
touch /var/log/supervisor/supervisord.log
chmod 640 /var/log/supervisor/supervisord.log
chown redis:redis /var/log/supervisor/supervisord.log

echo "📊 Iniciando Supervisor com serviços:"
echo "  - Redis Server"
echo "  - Connection Manager"

# Mostrar informações do sistema
echo "💽 Espaço em disco: $(df -h /data | tail -1 | awk '{print $4}')"

# Aguardar para garantir que o volume esteja montado
sleep 2

# Iniciar supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
