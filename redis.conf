
# Configuração básica do Redis
bind 0.0.0.0
port 6379
aclfile /etc/redis/users.acl

# Configurações de reconexão e keepalive
tcp-keepalive 60
timeout 0
replica-read-only yes
replica-serve-stale-data yes

# Logs para monitoramento
loglevel notice
logfile /data/redis.log

# Persistência (snapshots)
save 900 1
save 300 100
save 60 10000
rdbcompression yes
dbfilename dump.rdb
dir /data

# Limites e desempenho
maxmemory 400mb
maxmemory-policy allkeys-lru
maxclients 5000

# Buffers de saída para monitoramento
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit slave 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Databases
databases 2

# Persistência (AOF)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Segurança
protected-mode yes
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e9"

# Configurações de rede para reconexão
tcp-backlog 511
hz 10

# Slow log
slowlog-log-slower-than 20000
slowlog-max-len 128
