# Use uma variante Debian do Redis (tem apt-get)
FROM redis:8.2.0-bookworm

# Instalar dependências
RUN apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        python3 \
        python3-pip \
        python3-venv \
        supervisor \
        curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Criar usuário redis (se não existir)
RUN id -u redis >/dev/null 2>&1 || adduser --system --group --no-create-home redis

# Criar diretórios necessários
RUN mkdir -p /data /app /app/scripts /etc/supervisor/conf.d

# Criar ambiente virtual Python
RUN python3 -m venv /app/venv
RUN /app/venv/bin/pip install --no-cache-dir redis psutil

# Copiar arquivos de configuração
COPY redis.conf /etc/redis/redis.conf
COPY users.acl /etc/redis/users.acl
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY redis_connection_manager.py /app/scripts/redis_connection_manager.py
COPY entrypoint.sh /app/entrypoint.sh

# Tornar scripts executáveis
RUN chmod +x /app/entrypoint.sh

# Definir permissões iniciais
RUN chown -R redis:redis /data /etc/redis && \
    chmod 640 /etc/redis/redis.conf /etc/redis/users.acl

# Expor porta do Redis
EXPOSE 6379

# Definir entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
