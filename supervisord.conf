[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
loglevel=debug
user=root

[program:redis-server]
command=redis-server /etc/redis/redis.conf
autostart=true
autorestart=true
startretries=10
stdout_logfile=/data/redis.log
stderr_logfile=/data/redis_error.log
user=redis
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile_backups=3

[program:redis-connection-manager]
command=/app/venv/bin/python3 /app/scripts/redis_connection_manager.py
autostart=true
autorestart=true
startretries=10
stdout_logfile=/data/connection_manager.log
stderr_logfile=/data/connection_manager_error.log
user=root
depends_on=redis-server
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile_backups=3
