#!/usr/bin/env python3
"""
Redis Connection Manager - Gerencia reconexões automáticas dentro do container
"""
import redis
import time
import logging
import psutil
import subprocess
import os
import signal
import sys
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/data/redis_connection_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RedisConnectionManager:
    def __init__(self):
    # Permitir configurar via env; default para loopback dentro do container
    self.redis_host = os.getenv('REDIS_HOST', '127.0.0.1')
        self.redis_port = 6379
        self.redis_user = os.getenv('REDIS_USER')
        self.redis_password = os.getenv('REDIS_PASSWORD')
        if not self.redis_user or not self.redis_password:
            logger.error("REDIS_USER ou REDIS_PASSWORD não definidos")
            sys.exit(1)
        self.check_interval = 30
        self.client = None
        self.running = True
        self.restart_count = 0
        self.max_restarts = 5
        self.initial_connect_attempts = 5
        self.initial_connect_delay = 3

        # Configurar handlers para sinais
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)

    def signal_handler(self, signum, frame):
        """Handler para sinais de parada"""
        logger.info(f"Recebido sinal {signum}, parando gerenciador...")
        self.running = False
        sys.exit(0)

    def create_redis_client(self):
        """Cria cliente Redis com configurações de reconexão"""
        try:
            self.client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                username=self.redis_user,
                password=self.redis_password,
                decode_responses=True,
                socket_keepalive=True,
                socket_keepalive_options={
                    1: 1,   # TCP_KEEPIDLE
                    2: 3,   # TCP_KEEPINTVL
                    3: 5    # TCP_KEEPCNT
                },
                health_check_interval=30,
                socket_connect_timeout=10,
                socket_timeout=10,
                connection_pool=redis.ConnectionPool(
                    host=self.redis_host,
                    port=self.redis_port,
                    username=self.redis_user,
                    password=self.redis_password,
                    max_connections=50
                )
            )
            # Testar conexão com ping (lidando com estado LOADING e timeouts)
            quick_retries = 5
            for attempt in range(1, quick_retries + 1):
                try:
                    self.client.ping()
                    logger.info("Conexão ao Redis estabelecida com sucesso")
                    return True
                except redis.exceptions.BusyLoadingError:
                    # Redis está carregando o dataset em memória (RDB/AOF).
                    logger.info("Redis carregando dataset em memória (LOADING). Aguardando...")
                    time.sleep(2)
                except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
                    logger.warning(f"Conexão indisponível (tentativa rápida {attempt}/{quick_retries}): {e}")
                    time.sleep(2)
            # Se não conectou nas tentativas rápidas, falha para que o loop externo trate
            logger.error("Falha ao pingar o Redis após tentativas rápidas")
            return False
        except Exception as e:
            logger.error(f"Erro ao criar cliente Redis: {e}")
            return False

    def check_redis_health(self):
        """Verifica saúde do Redis"""
        try:
            if not self.client:
                return False

            # Ping básico
            pong = self.client.ping()
            if not pong:
                return False

            # Verifica info do servidor
            info = self.client.info('server')
            if not info:
                return False

            # Verifica se está aceitando conexões
            clients_info = self.client.info('clients')
            connected_clients = clients_info.get('connected_clients', 0)

            logger.info(f"Redis OK - Clientes conectados: {connected_clients}")
            return True
        except Exception as e:
            logger.error(f"Erro no health check: {e}")
            return False

    def check_redis_process(self):
        """Verifica se processo Redis está rodando"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                name = proc.info.get('name') or ''
                cmdline = ' '.join(proc.info.get('cmdline') or [])
                if 'redis-server' in name or 'redis-server' in cmdline:
                    return True, proc.info['pid']
            return False, None
        except Exception as e:
            logger.error(f"Erro ao verificar processo Redis: {e}")
            return False, None

    def restart_redis_if_needed(self):
        """Reinicia Redis se necessário"""
        try:
            is_running, pid = self.check_redis_process()

            if not is_running:
                logger.warning("Processo Redis não encontrado, tentando reiniciar...")
                self.restart_redis_server()
                return True

            # Se processo existe mas não responde, mata e reinicia
            if not self.check_redis_health():
                logger.warning(f"Redis não responde (PID: {pid}), reiniciando...")
                try:
                    os.kill(pid, signal.SIGTERM)
                    time.sleep(5)
                    if psutil.pid_exists(pid):
                        logger.warning("Processo ainda ativo, enviando SIGKILL")
                        os.kill(pid, signal.SIGKILL)
                except:
                    pass

                self.restart_redis_server()
                return True

            return False
        except Exception as e:
            logger.error(f"Erro ao verificar necessidade de restart: {e}")
            return False

    def restart_redis_server(self):
        """Reinicia servidor Redis"""
        try:
            if self.restart_count >= self.max_restarts:
                logger.error("Máximo de restarts atingido, parando gerenciador")
                self.running = False
                return False

            self.restart_count += 1
            logger.info(f"Reiniciando Redis (tentativa {self.restart_count}/{self.max_restarts})")

            # Comando para iniciar Redis
            cmd = ['redis-server', '/etc/redis/redis.conf']

            # Inicia processo Redis em background
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )

            # Aguarda alguns segundos para inicialização
            time.sleep(5)

            # Recria cliente Redis
            if self.create_redis_client():
                logger.info("Redis reiniciado com sucesso")
                self.restart_count = 0
                return True
            else:
                logger.error("Falha ao conectar após restart")
                return False
        except Exception as e:
            logger.error(f"Erro ao reiniciar Redis: {e}")
            return False

    def monitor_connections(self):
        """Monitora e gerencia conexões"""
        while self.running:
            try:
                # Verifica saúde geral
                if not self.check_redis_health():
                    logger.warning("Redis não está saudável")

                    # Tenta restart se necessário
                    if self.restart_redis_if_needed():
                        time.sleep(10)
                        continue

                # Monitora uso de memória
                try:
                    memory_info = self.client.info('memory')
                    used_memory = memory_info.get('used_memory', 0)
                    max_memory = memory_info.get('maxmemory', 0)

                    if max_memory > 0:
                        memory_usage = (used_memory / max_memory) * 100
                        if memory_usage > 90:
                            logger.warning(f"Uso de memória alto: {memory_usage:.1f}%")
                            try:
                                self.client.execute_command('CLIENT KILL TYPE NORMAL')
                                logger.info("Desconectados clientes inativos para liberar memória")
                            except Exception as e:
                                logger.error(f"Erro ao desconectar clientes: {e}")
                except Exception as e:
                    logger.error(f"Erro ao verificar memória: {e}")

                # Intervalo entre verificações
                time.sleep(self.check_interval)
            except KeyboardInterrupt:
                logger.info("Parando monitoramento...")
                break
            except Exception as e:
                logger.error(f"Erro no loop de monitoramento: {e}")
                time.sleep(self.check_interval)

    def run(self):
        """Inicia o gerenciador"""
        logger.info("Iniciando Redis Connection Manager")

        # Tenta conectar ao Redis com várias tentativas iniciais
        for attempt in range(self.initial_connect_attempts):
            if self.create_redis_client():
                logger.info("Conexão inicial ao Redis bem-sucedida")
                break
            logger.warning(f"Tentativa {attempt + 1}/{self.initial_connect_attempts} de conexão falhou")
            time.sleep(self.initial_connect_delay)
        else:
            logger.error("Falha ao criar cliente Redis inicial após várias tentativas")
            return False

        # Inicia monitoramento
        self.monitor_connections()

        logger.info("Redis Connection Manager finalizado")
        return True

if __name__ == "__main__":
    manager = RedisConnectionManager()
    manager.run()
